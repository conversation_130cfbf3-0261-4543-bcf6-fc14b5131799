# 办案中心值班管理系统 - 大屏显示页面修改方案

## 项目背景

当前系统需要将值班信息页面适配为前台大屏显示，供外部人员查看当前值班情况。需要隐藏详细值班表，突出显示当前值班人员和联系方式。

## 修改目标

### 主要需求

1. 适配大屏显示，确保屏幕越大内容也相应放大
2. 只显示新华和长安当前值班人员信息
3. 突出显示值班人员姓名和电话
4. 隐藏详细值班表
5. 保持现有日期计算逻辑不变

### 应用场景

- 前台大屏展示
- 供外部人员查看
- 24 小时实时显示
- 多种屏幕尺寸适配

## 页面设计方案

### 1. 布局结构调整

**原有布局问题：**

- 显示完整值班表，信息过于详细
- 字体大小固定，不适合大屏
- 布局复杂，不够简洁明了

**新布局设计：**

```
┌─────────────────────────────────────┐
│           系统标题 + 当前时间         │
├─────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐   │
│  │  新华组  │  │  长安组  │   │
│  │  第X组值班   │  │  第X组值班   │   │
│  │  带班民警：XXX │  │  带班民警：XXX │
│  │  值班电话：XXX │  │  值班电话：XXX │
│  └─────────────┘  └─────────────┘   │
├─────────────────────────────────────┤
```
新华组   x组
xxx        （对应手机号）
xxx         （对应手机号）
长安组   x组
xxx         (对应手机号)
xxx         (对应手机号)

### 2. 视觉设计优化

**字体大小策略：**

- 使用 `clamp()` CSS 函数实现响应式字体
- 标题：2rem ~ 4rem（根据屏幕宽度）
- 内容：1.4rem ~ 2.2rem
- 时间：1rem ~ 1.4rem

**颜色方案：**

- 背景：渐变色（蓝紫色系）
- 卡片：白色背景，圆角设计
- 新华：绿色顶部边框
- 长安：蓝色顶部边框
- 电话：红色突出显示

**响应式设计：**

- 大屏：横向并排显示两个卡片
- 小屏：纵向堆叠显示
- 超大屏：增加内边距和字体大小

### 3. 功能保持

**保留功能：**

- 日期计算逻辑完全保持不变
- 自动计算当前值班组
- 实时时间显示
- 值班人员信息映射

**移除功能：**

- 详细值班表显示
- 编辑和管理功能
- 复杂的交互操作

## 技术实现要点

### 1. 响应式设计

- 使用 `vw`、`vh` 单位确保大屏适配
- `clamp()` 函数实现字体自适应
- 媒体查询处理特殊屏幕尺寸

### 2. 数据逻辑

- 保持现有的 `calculateDutyGroup` 函数
- 维持值班人员和电话的映射关系
- 实时计算当前值班组

### 3. 性能优化

- 使用 `computed` 属性缓存计算结果
- 定时器管理时间更新
- 组件卸载时清理定时器

## 文件修改清单

### 需要修改的文件

1. `src/views/ScreenLayoutOne.vue` - 主要大屏显示页面
2. 可能需要修改 `src/views/Home.vue` - 如果首页也需要大屏适配

### 修改内容概述

1. **模板结构**：简化为卡片式布局
2. **样式系统**：全面响应式设计
3. **脚本逻辑**：保持计算逻辑，简化显示逻辑
4. **数据展示**：突出当前值班信息

## 预期效果

### 用户体验

- 信息一目了然，快速获取当前值班情况
- 大屏显示效果佳，远距离也能清晰阅读
- 自动更新，无需人工干预

### 技术效果

- 适配各种屏幕尺寸（手机到大屏）
- 性能稳定，长时间运行无问题
- 代码简洁，易于维护

## 实施步骤

### 第一阶段：页面结构调整

1. 修改 `src/views/ScreenLayoutOne.vue` 模板结构
2. 简化数据展示逻辑
3. 保持现有计算函数不变

### 第二阶段：样式优化

1. 实现响应式字体系统
2. 优化颜色和布局设计
3. 添加动画和交互效果

### 第三阶段：测试验证

1. 多种屏幕尺寸测试
2. 长时间运行稳定性测试
3. 用户体验验证

## 后续优化建议

1. **数据接口**：后期可接入后端 API 实现动态数据更新
2. **主题切换**：可增加日夜间模式切换
3. **多语言**：如需要可增加英文显示
4. **监控告警**：可增加系统状态监控显示

## 项目持续优化指南

本次修改主要解决了大屏显示适配问题，简化了用户界面，提升了信息展示效果。在实施过程中需要注意保持现有业务逻辑不变，确保值班信息的准确性和实时性。

后续可根据实际使用反馈进一步优化显示效果和交互体验。

---

**文档版本：** v1.0  
**创建日期：** 2024 年 12 月  
**最后更新：** 2024 年 12 月  
**负责人：** 开发团队
