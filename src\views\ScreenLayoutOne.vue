<template>
  <div class="screen-container" ref="screenContainer">
    <!-- 新华组 -->
    <div class="duty-section xinhua-section">
      <div class="group-title">
        <span class="district-name">新华组</span>
        <span class="group-number">第{{ currentXinhuaDutyGroup }}组</span>
      </div>
      <div class="duty-list">
        <div class="duty-item" v-for="(person, index) in xinhuaDutyPersons" :key="index">
          <span class="person-name">{{ person.name }}</span>
          <span class="person-phone">{{ person.phone }}</span>
        </div>
      </div>
    </div>

    <!-- 长安组 -->
    <div class="duty-section changan-section">
      <div class="group-title">
        <span class="district-name">长安组</span>
        <span class="group-number">第{{ currentChanganDutyGroup }}组</span>
      </div>
      <div class="duty-list">
        <div class="duty-item" v-for="(person, index) in changanDutyPersons" :key="index">
          <span class="person-name">{{ person.name }}</span>
          <div class="person-phones">
            <span class="person-phone">{{ person.phone }}</span>
            <span class="phone-separator">/</span>
            <span class="person-mobile">{{ person.mobile }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 时间显示与当前日期处理
const currentTime = ref(formatTime(new Date()));
const currentDateTime = ref(new Date());
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  const now = new Date();
  currentTime.value = formatTime(now);
  currentDateTime.value = now;
}

// 获取当前日期信息（使用响应式的当前时间）
const today = computed(() => currentDateTime.value);

// 创建一个通用函数计算值班组
function calculateDutyGroup(date, startDate, startGroup, totalGroups) {
  // 计算从起始日期到目标日期的天数差
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const baseDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const diffTime = targetDate.getTime() - baseDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // 基于天数差和起始组计算当前值班组
  // 例如：如果有4个组，起始组是2，则按照2->3->4->1->2->...的顺序循环
  const groupOffset = diffDays % totalGroups;
  let dutyGroup = (startGroup + groupOffset) % totalGroups;
  if (dutyGroup === 0) dutyGroup = totalGroups; // 如果余数为0，说明是最后一组
  
  return dutyGroup;
}

// 定义起始日期和组的配置
const xinhuaStartDate = new Date(2025, 6, 1); // 2025年7月1日
const xinhuaStartGroup = 2;  // 新华7月1日是2组值班
const totalGroups = 4;  // 总共4个组

const changanStartDate = new Date(2025, 6, 1); // 2025年7月1日
const changanStartGroup = 3; // 长安7月1日是3组值班

// 计算当前新华值班组（0点交班）
const currentXinhuaDutyGroup = computed(() => {
  return calculateDutyGroup(today.value, xinhuaStartDate, xinhuaStartGroup, totalGroups);
});

// 计算当前长安值班组（上午9点交班）
const currentChanganDutyGroup = computed(() => {
  const now = today.value;
  const currentHour = now.getHours();

  // 如果当前时间是0点到8点59分，使用前一天的日期计算值班组
  let calculationDate = new Date(now);
  if (currentHour < 9) {
    calculationDate.setDate(calculationDate.getDate() - 1);
  }

  return calculateDutyGroup(calculationDate, changanStartDate, changanStartGroup, totalGroups);
});

// 获取当前新华值班组的带班民警和电话
const currentXinhuaDutyLeader = computed(() => {
  const leaders = {
    1: "张英",
    2: "池海涛/叶晓翼",
    3: "刘志华/张海英",
    4: "赵伟力/梁华"
  };
  return leaders[currentXinhuaDutyGroup.value] || "";
});

const currentXinhuaDutyPhone = computed(() => {
  const phones = {
    1: "682323",
    2: "663052",
    3: "662430",
    4: "662407"
  };
  const phonesSecond = {
    1: "",  // 移除李蕊的电话
    2: "662929",
    3: "662930",
    4: "631851"
  };
  
  // 如果是第一组且没有第二个电话，就不显示分隔符
  if (currentXinhuaDutyGroup.value === 1) {
    return phones[1];
  }
  
  return `${phones[currentXinhuaDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentXinhuaDutyGroup.value] || ""}`;
});

// 获取当前长安值班组的带班民警和电话
const currentChanganDutyLeader = computed(() => {
  const leaders = {
    1: "李建财/张振峰",
    2: "侯新宇/夏云龙",
    3: "成峰林/解兵海",
    4: "曹聚刚/杨国伟"
  };
  return leaders[currentChanganDutyGroup.value] || "";
});

const currentChanganDutyPhone = computed(() => {
  const phones = {
    1: "669302,13903113965|665507,15133137737",
    2: "662713,13931979593|669574,13932170101",
    3: "680205,15830168080|662963,13931979710",
    4: "669695,15028136528|669694,13785172618"
  };
  const phoneStr = phones[currentChanganDutyGroup.value] || "";
  return phoneStr.replace(/\|/g, '<span class="phone-separator">|</span>');
});

// 解析新华组人员信息
const xinhuaDutyPersons = computed(() => {
  const names = currentXinhuaDutyLeader.value.split('/');
  const phoneStr = currentXinhuaDutyPhone.value.replace(/<span class="phone-separator">\|<\/span>/g, '|');
  const phones = phoneStr.split('|');

  const persons = [];
  for (let i = 0; i < names.length; i++) {
    if (names[i] && names[i].trim()) {
      persons.push({
        name: names[i].trim(),
        phone: phones[i] ? phones[i].trim() : ''
      });
    }
  }
  return persons;
});

// 解析长安组人员信息
const changanDutyPersons = computed(() => {
  const names = currentChanganDutyLeader.value.split('/');
  const phoneStr = currentChanganDutyPhone.value.replace(/<span class="phone-separator">\|<\/span>/g, '|');
  const phones = phoneStr.split('|');

  const persons = [];
  for (let i = 0; i < names.length; i++) {
    if (names[i] && names[i].trim()) {
      const phoneNumbers = phones[i] ? phones[i].split(',') : [];
      const shortPhone = phoneNumbers[0] || '';
      const mobilePhone = phoneNumbers[1] || '';

      persons.push({
        name: names[i].trim(),
        phone: shortPhone,
        mobile: mobilePhone
      });
    }
  }
  return persons;
});

// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
    }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: clamp(20px, 2vw, 60px);
  box-sizing: border-box;
  gap: clamp(20px, 2vh, 40px);
  overflow: hidden;
}

/* 值班区域样式 */
.duty-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: clamp(20px, 2vw, 40px);
  padding: clamp(20px, 3vh, 60px) clamp(30px, 4vw, 80px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.duty-section:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* 组标题样式 */
.group-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp(15px, 2vh, 40px);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  padding: 0 clamp(10px, 2vw, 30px);
}

/* 区域名称样式 */
.district-name {
  font-size: clamp(40px, 6vw, 120px);
  font-weight: 700;
  color: #ffffff;
  padding: clamp(8px, 1.5vh, 20px) clamp(15px, 3vw, 35px);
  border-radius: clamp(12px, 2vw, 25px);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* 新华组特定颜色 */
.xinhua-section .district-name {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* 长安组特定颜色 */
.changan-section .district-name {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

/* 组号样式 */
.group-number {
  font-size: clamp(35px, 5vw, 100px);
  font-weight: 600;
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: clamp(5px, 1vh, 15px) clamp(10px, 2vw, 25px);
  border-radius: clamp(8px, 1vw, 20px);
  border: 2px solid rgba(220, 38, 38, 0.3);
}

/* 人员列表样式 */
.duty-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: clamp(20px, 3vh, 50px);
  padding: 0 clamp(20px, 3vw, 60px);
}

/* 每个人员项样式 */
.duty-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: clamp(10px, 1.5vh, 25px) 0;
  border-bottom: 3px solid rgba(0, 0, 0, 0.1);
}

.duty-item:last-child {
  border-bottom: none;
}

/* 姓名样式 */
.person-name {
  font-size: clamp(35px, 5vw, 100px);
  font-weight: 700;
  color: #1f2937;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  text-align: left;
  flex: 1;
  word-break: break-all;
  white-space: normal;
  overflow-wrap: break-word;
}

/* 电话容器样式 */
.person-phones {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
  gap: clamp(8px, 1vw, 15px);
}

/* 短号样式 */
.person-phone {
  font-size: clamp(35px, 5vw, 100px);
  font-weight: 800;
  color: #dc2626;
  text-shadow: 3px 3px 6px rgba(220, 38, 38, 0.2);
  letter-spacing: 0.02em;
}

/* 手机号样式 */
.person-mobile {
  font-size: clamp(35px, 5vw, 100px);
  font-weight: 800;
  color: #059669;
  text-shadow: 3px 3px 6px rgba(5, 150, 105, 0.2);
  letter-spacing: 0.02em;
}

/* 分隔符样式 */
.phone-separator {
  font-size: clamp(35px, 5vw, 100px);
  font-weight: 600;
  color: #6b7280;
}

/* 4K屏幕优化 */
@media screen and (min-width: 3840px) and (min-height: 2160px) {
  .district-name {
    font-size: 180px;
    padding: 20px 40px;
    border-radius: 30px;
  }

  .group-number {
    font-size: 140px;
    padding: 15px 30px;
    border-radius: 25px;
  }

  .group-title {
    margin-bottom: 60px;
    padding: 0 80px;
  }

  .person-name {
    font-size: 140px;
  }

  .person-phone,
  .person-mobile,
  .phone-separator {
    font-size: 140px;
  }

  .person-phones {
    gap: 20px;
  }

  .duty-item {
    padding: 30px 0;
  }

  .duty-list {
    gap: 60px;
    padding: 0 80px;
  }
}

/* 超大屏幕优化 */
@media screen and (min-width: 1920px) and (max-width: 3839px) {
  .district-name {
    font-size: 120px;
    padding: 15px 30px;
    border-radius: 25px;
  }

  .group-number {
    font-size: 100px;
    padding: 12px 25px;
    border-radius: 20px;
  }

  .group-title {
    margin-bottom: 40px;
    padding: 0 60px;
  }

  .person-name {
    font-size: 100px;
  }

  .person-phone,
  .person-mobile,
  .phone-separator {
    font-size: 100px;
  }

  .person-phones {
    gap: 15px;
  }

  .duty-item {
    padding: 20px 0;
  }

  .duty-list {
    gap: 40px;
    padding: 0 60px;
  }
}

/* 中等屏幕 */
@media screen and (max-width: 1919px) {
  .district-name {
    font-size: clamp(50px, 7vw, 100px);
    padding: clamp(10px, 1.5vh, 15px) clamp(20px, 3vw, 25px);
    border-radius: clamp(15px, 2vw, 20px);
  }

  .group-number {
    font-size: clamp(40px, 6vw, 80px);
    padding: clamp(8px, 1vh, 12px) clamp(15px, 2vw, 20px);
    border-radius: clamp(10px, 1.5vw, 15px);
  }

  .group-title {
    padding: 0 clamp(15px, 2vw, 40px);
  }

  .person-name {
    font-size: clamp(30px, 5vw, 80px);
  }

  .person-phone,
  .person-mobile,
  .phone-separator {
    font-size: clamp(30px, 5vw, 80px);
  }

  .person-phones {
    gap: clamp(8px, 1vw, 12px);
  }

  .duty-list {
    gap: clamp(15px, 2vh, 30px);
    padding: 0 clamp(15px, 2vw, 40px);
  }
}

/* 小屏幕优化 */
@media screen and (max-width: 768px) {
  .screen-container {
    padding: 15px;
    gap: 15px;
  }

  .duty-section {
    padding: 20px;
  }

  .group-title {
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 0 10px;
  }

  .district-name {
    font-size: clamp(30px, 7vw, 60px);
    padding: 8px 15px;
    border-radius: 12px;
  }

  .group-number {
    font-size: clamp(25px, 6vw, 50px);
    padding: 5px 12px;
    border-radius: 8px;
  }

  .person-name {
    font-size: clamp(20px, 5vw, 40px);
  }

  .person-phone,
  .person-mobile,
  .phone-separator {
    font-size: clamp(20px, 5vw, 40px);
  }

  .person-phones {
    gap: clamp(5px, 1vw, 8px);
  }

  .duty-item {
    padding: 8px 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .person-name,
  .person-phone {
    text-align: left;
    flex: none;
  }

  .duty-list {
    gap: 15px;
    padding: 0 10px;
  }
}


</style> 