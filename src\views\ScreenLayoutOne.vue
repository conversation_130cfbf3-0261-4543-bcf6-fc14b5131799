<template>
  <div class="screen-container" ref="screenContainer">
    <!-- 新华组 -->
    <div class="duty-section xinhua-section">
      <div class="group-title">新华组</div>
      <div class="duty-name">{{ currentXinhuaDutyLeader }}</div>
      <div class="duty-phone" v-html="currentXinhuaDutyPhone"></div>
    </div>

    <!-- 长安组 -->
    <div class="duty-section changan-section">
      <div class="group-title">长安组</div>
      <div class="duty-name">{{ currentChanganDutyLeader }}</div>
      <div class="duty-phone" v-html="currentChanganDutyPhone"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 时间显示与当前日期处理
const currentTime = ref(formatTime(new Date()));
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  currentTime.value = formatTime(new Date());
}

// 获取当前日期信息
const today = new Date();

// 创建一个通用函数计算值班组
function calculateDutyGroup(date, startDate, startGroup, totalGroups) {
  // 计算从起始日期到目标日期的天数差
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const baseDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const diffTime = targetDate.getTime() - baseDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // 基于天数差和起始组计算当前值班组
  // 例如：如果有4个组，起始组是2，则按照2->3->4->1->2->...的顺序循环
  const groupOffset = diffDays % totalGroups;
  let dutyGroup = (startGroup + groupOffset) % totalGroups;
  if (dutyGroup === 0) dutyGroup = totalGroups; // 如果余数为0，说明是最后一组
  
  return dutyGroup;
}

// 定义起始日期和组的配置
const xinhuaStartDate = new Date(2025, 6, 1); // 2025年7月1日
const xinhuaStartGroup = 2;  // 新华7月1日是2组值班
const totalGroups = 4;  // 总共4个组

const changanStartDate = new Date(2025, 6, 1); // 2025年7月1日
const changanStartGroup = 3; // 长安7月1日是3组值班

// 计算当前新华值班组
const currentXinhuaDutyGroup = computed(() => {
  return calculateDutyGroup(today, xinhuaStartDate, xinhuaStartGroup, totalGroups);
});

// 计算当前长安值班组
const currentChanganDutyGroup = computed(() => {
  return calculateDutyGroup(today, changanStartDate, changanStartGroup, totalGroups);
});

// 获取当前新华值班组的带班民警和电话
const currentXinhuaDutyLeader = computed(() => {
  const leaders = {
    1: "张英",
    2: "池海涛/叶晓翼",
    3: "刘志华/张海英",
    4: "赵伟力/梁华"
  };
  return leaders[currentXinhuaDutyGroup.value] || "";
});

const currentXinhuaDutyPhone = computed(() => {
  const phones = {
    1: "682323",
    2: "663052",
    3: "662430",
    4: "662407"
  };
  const phonesSecond = {
    1: "",  // 移除李蕊的电话
    2: "662929",
    3: "662930",
    4: "631851"
  };
  
  // 如果是第一组且没有第二个电话，就不显示分隔符
  if (currentXinhuaDutyGroup.value === 1) {
    return phones[1];
  }
  
  return `${phones[currentXinhuaDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentXinhuaDutyGroup.value] || ""}`;
});

// 获取当前长安值班组的带班民警和电话
const currentChanganDutyLeader = computed(() => {
  const leaders = {
    1: "李建财/张振峰",
    2: "侯新宇/夏云龙",
    3: "成峰林/解兵海",
    4: "曹聚刚/杨国伟"
  };
  return leaders[currentChanganDutyGroup.value] || "";
});

const currentChanganDutyPhone = computed(() => {
  const phones = {
    1: "669302",
    2: "662713",
    3: "680205",
    4: "669695"
  };
  const phonesSecond = {
    1: "665507",
    2: "669574",
    3: "662963",
    4: "669694"
  };
  return `${phones[currentChanganDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentChanganDutyGroup.value] || ""}`;
});



// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
    }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 40px;
  box-sizing: border-box;
  gap: 80px;
}

/* 值班区域样式 */
.duty-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 40px;
  padding: 60px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  text-align: center;
  width: 100%;
  max-width: 1200px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.duty-section:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
}

/* 组标题样式 */
.group-title {
  font-size: 120px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* 姓名样式 */
.duty-name {
  font-size: 180px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 60px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1;
}

/* 电话样式 */
.duty-phone {
  font-size: 200px;
  font-weight: 800;
  color: #dc2626;
  text-shadow: 3px 3px 6px rgba(220, 38, 38, 0.2);
  line-height: 1;
  letter-spacing: 0.05em;
}

/* 4K屏幕优化 */
@media screen and (min-width: 3840px) and (min-height: 2160px) {
  .group-title {
    font-size: 200px;
  }

  .duty-name {
    font-size: 250px;
    margin-bottom: 80px;
  }

  .duty-phone {
    font-size: 280px;
  }

  .duty-section {
    padding: 100px;
    max-width: 1600px;
  }
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .group-title {
    font-size: 80px;
  }

  .duty-name {
    font-size: 100px;
    margin-bottom: 40px;
  }

  .duty-phone {
    font-size: 120px;
  }

  .duty-section {
    padding: 40px;
    max-width: 800px;
  }
}

@media screen and (max-width: 768px) {
  .screen-container {
    padding: 20px;
    gap: 40px;
  }

  .group-title {
    font-size: 60px;
  }

  .duty-name {
    font-size: 80px;
    margin-bottom: 30px;
  }

  .duty-phone {
    font-size: 90px;
  }

  .duty-section {
    padding: 30px;
  }
}


</style> 