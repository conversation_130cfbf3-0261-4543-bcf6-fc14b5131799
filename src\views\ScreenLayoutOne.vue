<template>
  <div class="screen-container" ref="screenContainer">
    <div class="content-container">
      <!-- 顶部 - 日期值班表 -->
      <div class="calendar-section">
        <!-- <h2>办案中心值班表</h2> -->
        <div class="calendar-container">
          <table class="duty-calendar">
            <thead>
              <tr>
                <th></th>
                <th>星期一</th>
                <th>星期二</th>
                <th>星期三</th>
                <th>星期四</th>
                <th>星期五</th>
                <th>星期六</th>
                <th>星期日</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="date-label">日期</td>
                <td></td>
                <td :class="{ 'today-cell': isTodayByDate(7, 1) }">2025.7.1</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 2) }">2025.7.2</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 3) }">2025.7.3</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 4) }">2025.7.4</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 5) }">2025.7.5</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 6) }">2025.7.6</td>
              </tr>
              <tr>
                <td class="center-label">值班组</td>
                <td></td>
                <td :class="{ 'today-duty': isTodayByDate(7, 1) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 2) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 3) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 4) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 5) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 6) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
              </tr>
              <tr>
                <td class="date-label">日期</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 7) }">2025.7.7</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 8) }">2025.7.8</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 9) }">2025.7.9</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 10) }">2025.7.10</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 11) }">2025.7.11</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 12) }">2025.7.12</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 13) }">2025.7.13</td>
              </tr>
              <tr>
                <td class="center-label">值班组</td>
                <td :class="{ 'today-duty': isTodayByDate(7, 7) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 8) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 9) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 10) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 11) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 12) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 13) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
              </tr>
              <tr>
                <td class="date-label">日期</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 14) }">2025.7.14</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 15) }">2025.7.15</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 16) }">2025.7.16</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 17) }">2025.7.17</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 18) }">2025.7.18</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 19) }">2025.7.19</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 20) }">2025.7.20</td>
              </tr>
              <tr>
                <td class="center-label">值班组</td>
                <td :class="{ 'today-duty': isTodayByDate(7, 14) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 15) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 16) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 17) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 18) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 19) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 20) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
              </tr>
              <tr>
                <td class="date-label">日期</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 21) }">2025.7.21</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 22) }">2025.7.22</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 23) }">2025.7.23</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 24) }">2025.7.24</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 25) }">2025.7.25</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 26) }">2025.7.26</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 27) }">2025.7.27</td>
              </tr>
              <tr>
                <td class="center-label">值班组</td>
                <td :class="{ 'today-duty': isTodayByDate(7, 21) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 22) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 23) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 24) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 25) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 26) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 27) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
              </tr>
              <tr>
                <td class="date-label">日期</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 28) }">2025.7.28</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 29) }">2025.7.29</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 30) }">2025.7.30</td>
                <td :class="{ 'today-cell': isTodayByDate(7, 31) }">2025.7.31</td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
              <tr>
                <td class="center-label">值班组</td>
                <td :class="{ 'today-duty': isTodayByDate(7, 28) }">
                  <div class="district-number xinhua">新华 1组</div>
                  <div class="district-number changan">长安 2组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 29) }">
                  <div class="district-number xinhua">新华 2组</div>
                  <div class="district-number changan">长安 3组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 30) }">
                  <div class="district-number xinhua">新华 3组</div>
                  <div class="district-number changan">长安 4组</div>
                </td>
                <td :class="{ 'today-duty': isTodayByDate(7, 31) }">
                  <div class="district-number xinhua">新华 4组</div>
                  <div class="district-number changan">长安 1组</div>
                </td>
                <td></td>
                <td></td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="current-duty-info">
          <div class="today-highlight">
            <div class="today-title-container">
              <span class="today-title">今日值班</span>
            </div>
            <div class="duty-content">
              <div class="duty-group">
                <span class="duty-district xinhua-district">新华</span>
                <span class="group-text">{{ currentXinhuaDutyGroup }}组</span> 
                <span class="duty-info">{{ currentXinhuaDutyLeader }} <span class="duty-separator">电话:</span> <strong v-html="currentXinhuaDutyPhone"></strong></span>
              </div>
              <div class="duty-group">
                <span class="duty-district changan-district">长安</span>
                <span class="group-text">{{ currentChanganDutyGroup }}组</span>
                <span class="duty-info">{{ currentChanganDutyLeader }} <span class="duty-separator">电话:</span> <strong v-html="currentChanganDutyPhone"></strong></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 值班人员详情 -->
      <div class="duty-details-container">
        <!-- 新华办案中心值班详情 -->
        <div class="duty-detail-section xinhua">
          <h2>新华办案中心值班详情</h2>
          <div class="table-container">
            <table class="staff-duty-table">
              <thead>
                <tr>
                  <th></th>
                  <th colspan="2" :class="{ 'active-group': currentXinhuaDutyGroup === 1 }">一组</th>
                  <th colspan="2" :class="{ 'active-group': currentXinhuaDutyGroup === 2 }">二组</th>
                  <th colspan="2" :class="{ 'active-group': currentXinhuaDutyGroup === 3 }">三组</th>
                  <th colspan="2" :class="{ 'active-group': currentXinhuaDutyGroup === 4 }">四组</th>
                  <th>备注</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="label">带班民警</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">张英</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }"></td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">池海涛</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">叶晓翼</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">刘志华</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">张海英</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">赵伟力</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">梁华</td>
                  <td rowspan="5">李蕊 681978</td>
                </tr>
                <tr>
                  <td class="label">值班电话</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">682323</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }"></td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">663052</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">662929</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">662430</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">662930</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">662407</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">631851</td>
                </tr>
                <tr>
                  <td class="label">前台登记</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">王晓蕾</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">郝亚飞</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">张月娟</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">张松</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">孙亚</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">石树三</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">张艳玲</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">王晓飞</td>
                </tr>
                <tr>
                  <td class="label">安全检查<br/>(穿插巡检)</td>
                  <td colspan="2" :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">
                    男：齐 壮<br/>
                    女：王晓蕾(兼)
                  </td>
                  <td colspan="2" :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">
                    男：闻 广<br/>
                    女：张月娟(兼)
                  </td>
                  <td colspan="2" :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">
                    男：牛 棚<br/>
                    女：孙亚(兼)
                  </td>
                  <td colspan="2" :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">
                    男：王志业<br/>
                    女：张艳玲(兼)
                  </td>
                </tr>
                <tr>
                  <td rowspan="3" class="label">候问看管</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">马来军</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">马军</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">刘冬伟</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">范兴隆<span class="sick-leave">(病休)</span></td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">李强</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">敦月强</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">杨立荣</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">王永召</td>
                </tr>
                <tr>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">刘卫彬</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">席秀兵</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">张达</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">狄祎</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">许彦芳</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">张猛<span class="sick-leave">(病休)</span></td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">宋继红</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">刘香宝</td>
                  <td rowspan="2"></td>
                </tr>
                <tr>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">吕沈锁</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 1 }">韦书华</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">高瑞峰</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 2 }">石树芳</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">刘国利</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 3 }">高建辉<span class="sick-leave">(病休)</span></td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">许海义</td>
                  <td :class="{ 'xinhua-duty-active': currentXinhuaDutyGroup === 4 }">赵龙</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- 长安办案中心值班详情 -->
        <div class="duty-detail-section changan">
          <h2>长安办案中心值班详情</h2>
          <div class="table-container">
            <table class="staff-duty-table">
              <thead>
                <tr>
                  <th width="45">班次</th>
                  <th width="240">带班民警/电话</th>
                  <th width="200">前台</th>
                  <th width="350">队员</th>
                </tr>
              </thead>
              <tbody>
                <tr :class="{ 'highlight-group': currentChanganDutyGroup === 1 }">
                  <td class="label">一班<br/>14人</td>
                  <td class="police-info">
                    <div>李建财 <span class="small-phone">669302</span><span class="mobile-phone">13903113965</span></div>
                    <div>张振峰 <span class="small-phone">665507</span><span class="mobile-phone">15133137737</span></div>
                  </td>
                  <td class="staff-list">
                    马耀彬、郭栋、李凯、巴雅伟、毕会哲
                  </td>
                  <td class="staff-list">
                    许晨博、王柯、钱森、鲍军乐、潘爱军、张保秋、阎晨光
                  </td>
                </tr>
                <tr :class="{ 'highlight-group': currentChanganDutyGroup === 2 }">
                  <td class="label">二班<br/>14人</td>
                  <td class="police-info">
                    <div>侯新宇 <span class="small-phone">662713</span><span class="mobile-phone">13931979593</span></div>
                    <div>夏云龙 <span class="small-phone">669574</span><span class="mobile-phone">13932170101</span></div>
                  </td>
                  <td class="staff-list">
                    杜自威、赵博、岳龙飞、李凤娇、王钰聪
                  </td>
                  <td class="staff-list">
                    张飞飞、蒋双军、马千里、王向辉、崔高祥、陈春勇、杨延伟
                  </td>
                </tr>
                <tr :class="{ 'highlight-group': currentChanganDutyGroup === 3 }">
                  <td class="label">三班<br/>13人</td>
                  <td class="police-info">
                    <div>成峰林 <span class="small-phone">680205</span><span class="mobile-phone">15830168080</span></div>
                    <div>解兵海 <span class="small-phone">662963</span><span class="mobile-phone">13931979710</span></div>
                  </td>
                  <td class="staff-list">
                    张泽、秦一鹤、李泽翔、李怡晴、陈红
                  </td>
                  <td class="staff-list">
                    刘英时、谢晓磊、任松亚、马学军、贾东升、梁小会
                  </td>
                </tr>
                <tr :class="{ 'highlight-group': currentChanganDutyGroup === 4 }">
                  <td class="label">四班<br/>14人</td>
                  <td class="police-info">
                    <div>曹聚刚 <span class="small-phone">669695</span><span class="mobile-phone">15028136528</span></div>
                    <div>杨国伟 <span class="small-phone">669694</span><span class="mobile-phone">13785172618</span></div>
                  </td>
                  <td class="staff-list">
                    郑亚敏、刘金泽、宋宗元、王慧、杨雯
                  </td>
                  <td class="staff-list">
                    祖添妍、张德君、赵亚鹏、冯昭贤、李泽豪、靳国政、赵士光
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 时间显示与当前日期处理
const currentTime = ref(formatTime(new Date()));
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  currentTime.value = formatTime(new Date());
}

// 获取当前日期信息
const today = new Date();
const currentMonth = today.getMonth() + 1;
const currentDay = today.getDate();
const currentYear = today.getFullYear();

// 创建一个通用函数计算值班组
function calculateDutyGroup(date, startDate, startGroup, totalGroups) {
  // 计算从起始日期到目标日期的天数差
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const baseDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const diffTime = targetDate.getTime() - baseDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // 基于天数差和起始组计算当前值班组
  // 例如：如果有4个组，起始组是2，则按照2->3->4->1->2->...的顺序循环
  const groupOffset = diffDays % totalGroups;
  let dutyGroup = (startGroup + groupOffset) % totalGroups;
  if (dutyGroup === 0) dutyGroup = totalGroups; // 如果余数为0，说明是最后一组
  
  return dutyGroup;
}

// 定义起始日期和组的配置
const xinhuaStartDate = new Date(2025, 6, 1); // 2025年7月1日
const xinhuaStartGroup = 2;  // 新华7月1日是2组值班
const totalGroups = 4;  // 总共4个组

const changanStartDate = new Date(2025, 6, 1); // 2025年7月1日
const changanStartGroup = 3; // 长安7月1日是3组值班

// 计算当前新华值班组
const currentXinhuaDutyGroup = computed(() => {
  return calculateDutyGroup(today, xinhuaStartDate, xinhuaStartGroup, totalGroups);
});

// 计算当前长安值班组
const currentChanganDutyGroup = computed(() => {
  return calculateDutyGroup(today, changanStartDate, changanStartGroup, totalGroups);
});

// 获取当前新华值班组的带班民警和电话
const currentXinhuaDutyLeader = computed(() => {
  const leaders = {
    1: "张英",
    2: "池海涛/叶晓翼",
    3: "刘志华/张海英",
    4: "赵伟力/梁华"
  };
  return leaders[currentXinhuaDutyGroup.value] || "";
});

const currentXinhuaDutyPhone = computed(() => {
  const phones = {
    1: "682323",
    2: "663052",
    3: "662430",
    4: "662407"
  };
  const phonesSecond = {
    1: "",  // 移除李蕊的电话
    2: "662929",
    3: "662930",
    4: "631851"
  };
  
  // 如果是第一组且没有第二个电话，就不显示分隔符
  if (currentXinhuaDutyGroup.value === 1) {
    return phones[1];
  }
  
  return `${phones[currentXinhuaDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentXinhuaDutyGroup.value] || ""}`;
});

// 获取当前长安值班组的带班民警和电话
const currentChanganDutyLeader = computed(() => {
  const leaders = {
    1: "李建财/张振峰",
    2: "侯新宇/夏云龙",
    3: "成峰林/解兵海",
    4: "曹聚刚/杨国伟"
  };
  return leaders[currentChanganDutyGroup.value] || "";
});

const currentChanganDutyPhone = computed(() => {
  const phones = {
    1: "669302",
    2: "662713",
    3: "680205",
    4: "669695"
  };
  const phonesSecond = {
    1: "665507",
    2: "669574",
    3: "662963",
    4: "669694"
  };
  return `${phones[currentChanganDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentChanganDutyGroup.value] || ""}`;
});

// 检查是否是今天 - 检查年、月和日
function isTodayByDate(month, day) {
  return currentDay === day && currentMonth === month && 
         (currentYear === 2025 || month !== 7); // 考虑特殊情况：如果是7月且不是2025年，则不匹配
}

// 更完善的日期检查函数，用于高亮显示
function isTodayFull(year, month, day) {
  return currentDay === day && currentMonth === month && currentYear === year;
}

// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
    }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  color: #f5f5f7;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  letter-spacing: -0.02em;
}

.content-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  /* padding: 15px; */
  gap: 15px;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
}

/* 日历表格样式 */
.calendar-section {
  flex: 0 0 auto; /* 不伸缩，保持原有尺寸 */
  background-color: rgba(30, 30, 30, 0.7);
  border-radius: 16px;
  padding: 18px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.35);
  margin-bottom: 0; /* 移除底部边距，由flex gap控制 */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.calendar-section h2 {
  color: #2997ff;
  text-align: center;
  font-size: 22px;
  margin-bottom: 15px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  font-weight: 500;
}

.calendar-container {
  overflow-x: auto;
}

.duty-calendar {
  width: 100%;
  border-collapse: separate;
  border-spacing: 2px;
  table-layout: fixed;
}

.duty-calendar th, 
.duty-calendar td {
  border: none;
  padding: 10px;
  text-align: center;
  font-size: 20px;
  min-width: 80px;
  border-radius: 8px;
  background-color: rgba(40, 40, 40, 0.5);
  transition: all 0.2s ease;
}

.duty-calendar th {
  background-color: rgba(60, 60, 60, 0.7);
  color: #f5f5f7;
  font-weight: 500;
  padding: 12px 8px;
  letter-spacing: 0.02em;
}

.date-label {
  color: #f56300;
  font-weight: 500;
}

.center-label {
  color: #2997ff;
  font-weight: 500;
}

/* 高亮今天的单元格 */
.today-cell {
  background-color: rgba(41, 151, 255, 0.2);
  font-weight: 500;
  box-shadow: 0 0 0 1px rgba(41, 151, 255, 0.7);
  color: #2997ff;
}

/* 高亮当天值班组 */
.today-duty {
  color: #2997ff;
  font-weight: 500;
  background-color: rgba(41, 151, 255, 0.15);
}

.district-number {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.08);
  border: none;
  font-size: 21px;
  margin: 3px 2px;
  font-weight: 500;
}

.district-number.xinhua {
  color: #2997ff;
  background-color: rgba(41, 151, 255, 0.15);
}

.district-number.changan {
  color: #f56300;
  background-color: rgba(245, 99, 0, 0.15);
}

.current-duty-info {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  width: 100%;
}

.today-highlight {
  background-color: rgba(40, 40, 40, 0.7);
  padding: 0;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 500;
  border: none;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: row;
  align-items: stretch;
  flex-wrap: nowrap;
  width: 100%;
  max-width: 1200px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  margin: 0 auto;
}

.today-title-container {
  background: linear-gradient(135deg, #f56300, #e11d48);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
  width: 130px;
  border-radius: 0;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

.today-title {
  font-size: 22px;
  color: #ffffff;
  white-space: nowrap;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.duty-content {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 0 20px;
}

.duty-group {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.duty-district {
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 8px;
  margin: 0 12px 0 0;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.xinhua-district {
  color: #ffffff;
  background: linear-gradient(135deg, #2997ff, #2179ff);
}

.changan-district {
  color: #ffffff;
  background: linear-gradient(135deg, #f56300, #ff375f);
}

.group-text {
  font-weight: 500;
  padding: 5px 12px;
  border-radius: 8px;
  margin: 0 10px;
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.12);
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.duty-info {
  font-size: 16px;
  color: #f5f5f7;
  font-weight: 500;
  margin: 0 8px 0 2px;
  white-space: nowrap;
}

.duty-info strong {
  color: #ffffff;
  text-decoration: none;
  letter-spacing: 0;
  background-color: rgba(60, 60, 60, 0.8);
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  white-space: nowrap;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.duty-separator {
  color: #8e8e93;
  font-size: 15px;
  font-weight: normal;
  margin: 0 6px;
}

/* 添加电话号码分割线样式 */
.phone-separator {
  display: inline-block;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 300;
  margin: 0 8px;
  vertical-align: middle;
  font-size: 15px;
}

/* 隐藏一些不需要的类 */
.duty-title,
.duty-centers,
.duty-center,
.center-header,
.center-name,
.duty-members,
.duty-member,
.duty-phone {
  display: none;
}

/* 值班详情容器样式 */
.duty-details-container {
  display: flex;
  flex: 1 1 auto; /* 允许伸缩，占据剩余空间 */
  gap: 15px;
  overflow: hidden;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
}

.duty-detail-section {
  flex: 1;
  background-color: rgba(30, 30, 30, 0.7);
  border-radius: 16px;
  padding: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.35);
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.duty-detail-section h2 {
  color: #2997ff;
  text-align: center;
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  flex-shrink: 0; /* 防止标题被压缩 */
  font-weight: 500;
}

.table-container {
  flex: 1 1 auto; /* 允许伸缩，占据剩余空间 */
  overflow: auto;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保在flex布局中可以正确收缩 */
}

/* 表格样式 */
.staff-duty-table {
  width: 100%;
  height: 100%; /* 确保表格填充整个容器高度 */
  border-collapse: separate;
  border-spacing: 2px;
  table-layout: fixed;
}

.staff-duty-table th,
.staff-duty-table td {
  border: none;
  padding: 8px;
  text-align: center;
  font-size: 17px;
  vertical-align: middle;
  border-radius: 8px;
  background-color: rgba(50, 50, 50, 0.5);
  transition: all 0.2s ease;
}

.staff-duty-table th {
  background-color: rgba(60, 60, 60, 0.7);
  color: #2997ff;
  font-weight: 500;
  padding: 10px 8px;
  letter-spacing: 0.02em;
}

.staff-duty-table .active-group {
  background: linear-gradient(135deg, rgba(41, 151, 255, 0.6), rgba(41, 151, 255, 0.4));
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(41, 151, 255, 0.3);
}

.staff-duty-table .label {
  background-color: rgba(60, 60, 60, 0.7);
  color: #2997ff;
  font-weight: 500;
}

.sick-leave {
  color: #ff375f;
  font-size: 12px;
  font-weight: normal;
}

/* 高亮当前值班组 */
.staff-duty-table tr.highlight-group {
  background-color: rgba(245, 99, 0, 0.15) !important; /* Apple橙色背景 */
  border-left: none !important;
  box-shadow: 0 0 0 1px rgba(245, 99, 0, 0.4) !important;
}

/* 增强选择器特异性，确保长安表格的高亮生效 */
.changan .staff-duty-table tr.highlight-group td {
  background-color: rgba(245, 99, 0, 0.15) !important;
}

/* 长安办案中心表格特有样式 */
.changan .staff-duty-table {
  width: 100%;
  height: 100%;
}

.changan .staff-duty-table td {
  text-align: left;
  padding: 6px 8px;
  line-height: 1.3;
  vertical-align: middle; /* 垂直居中 */
  font-size: 17px;
}

.changan .staff-duty-table th {
  text-align: center; /* 表头居中 */
  vertical-align: middle;
  padding: 8px 6px;
  font-size: 13px;
}

/* 确保长安办案中心的表格行自适应高度 */
.changan .staff-duty-table tr {
  height: 25%; /* 四个班组，每个占25%高度 */
}

.changan .staff-duty-table tr:nth-child(even) {
  background-color: rgba(51, 65, 85, 0.3); /* 增加隔行颜色 */
}

/* 带班民警和电话样式 */
.police-info {
  font-size: 13px;
  line-height: 1.4;
}

.police-info div {
  margin: 2px 0;
  white-space: nowrap;
}

.small-phone {
  color: #2997ff;
  font-weight: 500;
  background-color: rgba(41, 151, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 4px;
  font-size: 17px;
  display: inline-block;
}

.mobile-phone {
  color: #f56300;
  font-weight: 500;
  background-color: rgba(245, 99, 0, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 3px;
  font-size: 17px;
  display: inline-block;
}

.staff-list {
  font-size: 12px;
  line-height: 1.3;
  padding: 4px 6px;
  word-spacing: 1px;
}

.duty-phone {
  color: #38bdf8;
  font-weight: bold;
  margin-left: 5px;
  background-color: rgba(56, 189, 248, 0.1);
  padding: 2px 5px;
  border-radius: 4px;
  display: inline-block;
}

/* 新华办案中心表格特有样式 */
.xinhua .staff-duty-table {
  width: 100%;
  height: 100%;
}

.xinhua .staff-duty-table td {
  text-align: center;
  padding: 8px 5px; /* 稍微调整内边距 */
  vertical-align: middle;
}

/* 新华办案中心值班人员高亮 */
.xinhua-duty-active {
  background: linear-gradient(135deg, rgba(41, 151, 255, 0.6), rgba(41, 151, 255, 0.4));
  font-weight: 500;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 8px rgba(41, 151, 255, 0.3);
}

/* 自适应高度分配 */
@media screen and (min-height: 768px) {
  .calendar-section {
    flex: 0 0 auto; /* 不伸缩 */
  }
  
  .duty-details-container {
    flex: 1 1 auto; /* 占据所有剩余空间 */
  }
}

@media screen and (max-height: 767px) {
  .calendar-section {
    flex: 0 0 auto; /* 更紧凑布局 */
    padding: 10px;
  }
  
  .duty-details-container {
    flex: 1 1 auto;
  }
  
  .duty-detail-section {
    padding: 10px;
  }
  
  .duty-detail-section h2 {
    margin-bottom: 10px;
    padding-bottom: 5px;
  }
}

/* 响应式调整 */
@media screen and (max-height: 900px) {
  .staff-duty-table th,
  .staff-duty-table td {
    padding: 6px 4px;
    font-size: 13px;
  }
  
  .changan .staff-duty-table td {
    padding: 5px 3px;
    line-height: 1.3;
  }
  
  .police-info {
    font-size: 11px;
    line-height: 1.3;
  }
  
  .police-info div {
    margin: 1px 0;
  }
  
  .small-phone, .mobile-phone {
    font-size: 10px;
    padding: 1px 2px;
    margin-left: 2px;
  }
}

@media screen and (max-height: 800px) {
  .staff-duty-table th,
  .staff-duty-table td {
    padding: 4px 3px;
    font-size: 12px;
  }
  
  .duty-detail-section h2 {
    font-size: 16px;
    margin-bottom: 6px;
    padding-bottom: 6px;
  }
  
  .changan .staff-duty-table td {
    padding: 3px 2px;
    line-height: 1.2;
  }
  
  .police-info {
    font-size: 10px;
    line-height: 1.2;
  }
  
  .police-info div {
    margin: 0;
  }
  
  .small-phone, .mobile-phone {
    font-size: 9px;
    padding: 1px 2px;
    margin-left: 1px;
  }
  
  .staff-list {
    font-size: 10px;
    padding: 2px;
  }
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 128, 0.4);
  border-radius: 5px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 128, 0.6);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* 表格容器滚动条微调 */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.5);
}

.table-container::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 128, 0.4);
}

/* 大屏幕适配 - 让今日值班模块更大 */
@media screen and (min-width: 1440px) {  
  .today-title-container {
    width: 150px;
    padding: 18px 20px;
  }
  
  .today-title {
    font-size: 24px;
  }
  
  .duty-district {
    font-size: 18px;
    padding: 6px 12px;
  }
  
  .group-text {
    font-size: 18px;
    padding: 6px 12px;
  }
  
  .duty-info {
    font-size: 30px;
  }
  
  .duty-info strong {
    font-size: 30px;
    padding: 6px 10px;
  }
  
  .duty-separator {
    font-size: 16px;
    margin: 0 6px;
  }
  
  .phone-separator {
    font-size: 16px;
    margin: 0 8px;
  }
}

/* 更大的屏幕尺寸 */
@media screen and (min-width: 1920px) {
  .today-highlight {
    padding: 20px 40px;
    max-width: none;
  }
  
  .today-title {
    font-size: 30px;
  }
  
  .duty-district {
    font-size: 28px;
    padding: 4px 10px;
  }
  
  .group-text {
    font-size: 28px;
    padding: 4px 10px;
  }
  
  .duty-info {
    font-size: 24px;
  }
  
  .duty-info strong {
    font-size: 26px;
    padding: 4px 10px;
  }
}
</style> 