<template>
  <div class="screen-container" ref="screenContainer">
    <!-- 系统标题和时间 -->
    <div class="header-section">
      <h1 class="system-title">办案中心值班管理系统</h1>
      <div class="current-time">{{ currentTime }}</div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 新华办案中心卡片 -->
      <div class="duty-card xinhua-card">
        <div class="card-header xinhua-header">
          <h2 class="center-name">新华办案中心</h2>
          <div class="duty-group-badge">第{{ currentXinhuaDutyGroup }}组值班</div>
        </div>
        <div class="card-content">
          <div class="duty-leader">
            <span class="label">带班民警：</span>
            <span class="leader-name">{{ currentXinhuaDutyLeader }}</span>
          </div>
          <div class="duty-phone">
            <span class="label">值班电话：</span>
            <span class="phone-number" v-html="currentXinhuaDutyPhone"></span>
          </div>
        </div>
      </div>

      <!-- 长安办案中心卡片 -->
      <div class="duty-card changan-card">
        <div class="card-header changan-header">
          <h2 class="center-name">长安办案中心</h2>
          <div class="duty-group-badge">第{{ currentChanganDutyGroup }}组值班</div>
        </div>
        <div class="card-content">
          <div class="duty-leader">
            <span class="label">带班民警：</span>
            <span class="leader-name">{{ currentChanganDutyLeader }}</span>
          </div>
          <div class="duty-phone">
            <span class="label">值班电话：</span>
            <span class="phone-number" v-html="currentChanganDutyPhone"></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部提示信息 -->
    <div class="footer-info">
      <p class="notice-text">如需联系值班人员，请拨打上述电话号码</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

// 时间显示与当前日期处理
const currentTime = ref(formatTime(new Date()));
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  currentTime.value = formatTime(new Date());
}

// 获取当前日期信息
const today = new Date();

// 创建一个通用函数计算值班组
function calculateDutyGroup(date, startDate, startGroup, totalGroups) {
  // 计算从起始日期到目标日期的天数差
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const baseDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const diffTime = targetDate.getTime() - baseDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  
  // 基于天数差和起始组计算当前值班组
  // 例如：如果有4个组，起始组是2，则按照2->3->4->1->2->...的顺序循环
  const groupOffset = diffDays % totalGroups;
  let dutyGroup = (startGroup + groupOffset) % totalGroups;
  if (dutyGroup === 0) dutyGroup = totalGroups; // 如果余数为0，说明是最后一组
  
  return dutyGroup;
}

// 定义起始日期和组的配置
const xinhuaStartDate = new Date(2025, 6, 1); // 2025年7月1日
const xinhuaStartGroup = 2;  // 新华7月1日是2组值班
const totalGroups = 4;  // 总共4个组

const changanStartDate = new Date(2025, 6, 1); // 2025年7月1日
const changanStartGroup = 3; // 长安7月1日是3组值班

// 计算当前新华值班组
const currentXinhuaDutyGroup = computed(() => {
  return calculateDutyGroup(today, xinhuaStartDate, xinhuaStartGroup, totalGroups);
});

// 计算当前长安值班组
const currentChanganDutyGroup = computed(() => {
  return calculateDutyGroup(today, changanStartDate, changanStartGroup, totalGroups);
});

// 获取当前新华值班组的带班民警和电话
const currentXinhuaDutyLeader = computed(() => {
  const leaders = {
    1: "张英",
    2: "池海涛/叶晓翼",
    3: "刘志华/张海英",
    4: "赵伟力/梁华"
  };
  return leaders[currentXinhuaDutyGroup.value] || "";
});

const currentXinhuaDutyPhone = computed(() => {
  const phones = {
    1: "682323",
    2: "663052",
    3: "662430",
    4: "662407"
  };
  const phonesSecond = {
    1: "",  // 移除李蕊的电话
    2: "662929",
    3: "662930",
    4: "631851"
  };
  
  // 如果是第一组且没有第二个电话，就不显示分隔符
  if (currentXinhuaDutyGroup.value === 1) {
    return phones[1];
  }
  
  return `${phones[currentXinhuaDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentXinhuaDutyGroup.value] || ""}`;
});

// 获取当前长安值班组的带班民警和电话
const currentChanganDutyLeader = computed(() => {
  const leaders = {
    1: "李建财/张振峰",
    2: "侯新宇/夏云龙",
    3: "成峰林/解兵海",
    4: "曹聚刚/杨国伟"
  };
  return leaders[currentChanganDutyGroup.value] || "";
});

const currentChanganDutyPhone = computed(() => {
  const phones = {
    1: "669302",
    2: "662713",
    3: "680205",
    4: "669695"
  };
  const phonesSecond = {
    1: "665507",
    2: "669574",
    3: "662963",
    4: "669694"
  };
  return `${phones[currentChanganDutyGroup.value] || ""}<span class="phone-separator">|</span>${phonesSecond[currentChanganDutyGroup.value] || ""}`;
});



// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
    }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  letter-spacing: -0.02em;
  padding: clamp(20px, 3vw, 60px);
  box-sizing: border-box;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: clamp(30px, 5vh, 80px);
  flex-shrink: 0;
}

.system-title {
  font-size: clamp(2rem, 6vw, 5rem);
  font-weight: 700;
  margin: 0 0 clamp(15px, 2vh, 30px) 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.02em;
}

.current-time {
  font-size: clamp(1.2rem, 3vw, 2.5rem);
  font-weight: 500;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex: 1;
  gap: clamp(30px, 5vw, 80px);
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 值班卡片样式 */
.duty-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: clamp(16px, 2vw, 32px);
  padding: clamp(30px, 4vw, 60px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  flex: 1;
  min-width: clamp(300px, 40vw, 500px);
  max-width: 600px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.duty-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

/* 卡片头部样式 */
.card-header {
  text-align: center;
  margin-bottom: clamp(20px, 3vh, 40px);
  padding-bottom: clamp(15px, 2vh, 25px);
  border-bottom: 3px solid;
  position: relative;
}

.xinhua-header {
  border-bottom-color: #10b981;
}

.changan-header {
  border-bottom-color: #3b82f6;
}

.center-name {
  font-size: clamp(1.8rem, 4vw, 3.5rem);
  font-weight: 700;
  margin: 0 0 clamp(10px, 1.5vh, 20px) 0;
  color: #1f2937;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.duty-group-badge {
  display: inline-block;
  background: linear-gradient(135deg, #f59e0b, #f97316);
  color: white;
  padding: clamp(8px, 1.5vw, 16px) clamp(16px, 3vw, 32px);
  border-radius: clamp(20px, 2vw, 40px);
  font-size: clamp(1.2rem, 2.5vw, 2rem);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* 卡片内容样式 */
.card-content {
  padding-top: clamp(10px, 2vh, 20px);
}

.duty-leader,
.duty-phone {
  margin-bottom: clamp(20px, 3vh, 35px);
  text-align: center;
}

.label {
  display: block;
  font-size: clamp(1.2rem, 2.5vw, 2rem);
  font-weight: 600;
  color: #6b7280;
  margin-bottom: clamp(8px, 1vh, 15px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.leader-name {
  display: block;
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: 700;
  color: #1f2937;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.phone-number {
  display: block;
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  color: #dc2626;
  text-shadow: 2px 2px 4px rgba(220, 38, 38, 0.2);
  letter-spacing: 0.1em;
  line-height: 1.2;
}

/* 电话号码分隔符 */
.phone-number .phone-separator {
  color: #6b7280;
  font-weight: 400;
  margin: 0 clamp(8px, 1vw, 16px);
}

/* 底部信息样式 */
.footer-info {
  text-align: center;
  margin-top: clamp(30px, 4vh, 60px);
  flex-shrink: 0;
}

.notice-text {
  font-size: clamp(1rem, 2vw, 1.8rem);
  font-weight: 500;
  opacity: 0.9;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    gap: clamp(20px, 4vw, 40px);
  }

  .duty-card {
    max-width: 90vw;
    min-width: 300px;
  }
}

@media screen and (max-width: 768px) {
  .screen-container {
    padding: clamp(15px, 3vw, 30px);
  }

  .system-title {
    font-size: clamp(1.5rem, 8vw, 3rem);
  }

  .current-time {
    font-size: clamp(1rem, 4vw, 1.5rem);
  }

  .center-name {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
  }

  .duty-group-badge {
    font-size: clamp(1rem, 4vw, 1.5rem);
    padding: clamp(6px, 2vw, 12px) clamp(12px, 4vw, 24px);
  }

  .leader-name {
    font-size: clamp(1.5rem, 6vw, 2.5rem);
  }

  .phone-number {
    font-size: clamp(1.8rem, 7vw, 3rem);
  }
}

/* 超大屏幕优化 */
@media screen and (min-width: 1920px) {
  .system-title {
    font-size: clamp(3rem, 6vw, 6rem);
  }

  .current-time {
    font-size: clamp(2rem, 3vw, 3rem);
  }

  .center-name {
    font-size: clamp(2.5rem, 4vw, 4rem);
  }

  .duty-group-badge {
    font-size: clamp(1.8rem, 2.5vw, 2.5rem);
    padding: clamp(12px, 1.5vw, 20px) clamp(24px, 3vw, 40px);
  }

  .leader-name {
    font-size: clamp(3rem, 4vw, 4.5rem);
  }

  .phone-number {
    font-size: clamp(3.5rem, 5vw, 5rem);
  }

  .label {
    font-size: clamp(1.5rem, 2.5vw, 2.5rem);
  }

  .notice-text {
    font-size: clamp(1.5rem, 2vw, 2.2rem);
  }
}


</style> 