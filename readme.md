# 办案中心值班管理系统

这是一个用于管理办案中心值班表和显示大屏幕信息的系统。系统分为两个主要部分：大屏展示和后台管理。
长安早上九点交班

## 项目概述

办案中心值班管理系统用于展示和管理值班表、人员信息、规章制度等内容。系统支持两个大屏显示，分别展示不同内容，管理员可以通过后台管理系统进行配置和管理。

## 系统功能

### 大屏展示

- **屏幕 1**: 主要显示值班表、人员信息和相关通知
- **屏幕 2**: 主要显示规章制度、工作流程和其他信息

### 后台管理

- **登录认证**: 管理员登录系统
- **控制面板**: 系统概况和快捷操作
- **值班表管理**:
  - 值班表排班管理
  - 值班表预览
- **人员管理**:
  - 新华人员管理
  - 长安人员管理
- **屏幕管理**: 配置两个大屏的显示内容和样式
- **系统设置**:
  - 基本设置
  - 规章制度管理
  - 值班设置
  - 通知设置
  - 账户管理
- **个人中心**: 管理员个人信息管理

## 技术栈

### 前端

- **框架**: Vue 3
- **UI 组件库**: Element Plus
- **路由管理**: Vue Router
- **状态管理**: Vue Reactive API
- **HTTP 请求**: Axios

### 后端

- **框架**: SpringBoot 3
- **数据库**: MySQL 8
- **安全认证**: Spring Security + JWT
- **ORM**: Spring Data JPA
- **API 文档**: SpringDoc OpenAPI (Swagger)

## 项目结构

### 前端

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── layouts/         # 布局组件
│   └── AdminLayout.vue  # 后台管理布局
├── router/          # 路由配置
├── utils/           # 工具函数
├── views/           # 页面组件
│   ├── Home.vue              # 首页
│   ├── Login.vue             # 登录页
│   ├── ScreenLayoutOne.vue   # 大屏1
│   ├── ScreenLayoutTwo.vue   # 大屏2
│   └── admin/               # 后台管理页面
│       ├── Dashboard.vue     # 控制面板
│       ├── DutySchedule.vue  # 值班表排班
│       ├── StaffManagement.vue # 人员管理
│       ├── ScreensManagement.vue # 屏幕管理
│       ├── Settings.vue      # 系统设置
│       └── UserProfile.vue   # 个人资料
├── App.vue          # 根组件
└── main.js          # 入口文件
```

### 后端

```
backend/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── dutymanage/
│   │   │           └── api/
│   │   │               ├── config/         # 配置类
│   │   │               ├── controller/     # 控制器
│   │   │               ├── dto/            # 数据传输对象
│   │   │               ├── entity/         # 实体类
│   │   │               ├── exception/      # 异常处理
│   │   │               ├── repository/     # 数据访问层
│   │   │               ├── security/       # 安全配置
│   │   │               │   ├── jwt/        # JWT相关
│   │   │               │   └── services/   # 安全服务
│   │   │               ├── service/        # 业务逻辑层
│   │   │               ├── util/           # 工具类
│   │   │               └── DutyManagementApplication.java # 启动类
│   │   └── resources/
│   │       ├── application.properties  # 应用配置
│   │       ├── static/                # 静态资源
│   │       └── templates/             # 模板文件
│   └── test/                         # 测试代码
└── pom.xml                           # Maven配置
```

## 安装和运行

### 前端

```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 构建生产版
npm run build
```

### 后端

```bash
# 进入后端目录
cd backend

# 使用Maven编译
mvn clean package

# 运行Spring Boot应用
java -jar target/duty-management-api-0.0.1-SNAPSHOT.jar
```

## 使用说明

1. 启动后端服务
2. 启动前端开发服务器或部署前端构建文件
3. 访问系统：
   - 前端开发模式: http://localhost:5173
   - 后端 API: http://localhost:8080/api
4. 使用默认管理员账号登录:
   - 用户名: admin
   - 密码: 123456

## API 接口

### 用户认证

- `POST /api/auth/login`: 用户登录
- `POST /api/auth/logout`: 用户登出
- `GET /api/auth/profile`: 获取用户资料

### 值班表管理

- `GET /api/duty/schedule`: 获取值班表
- `POST /api/duty/schedule`: 创建值班表
- `PUT /api/duty/schedule/:id`: 更新值班表
- `DELETE /api/duty/schedule/:id`: 删除值班表

### 人员管理

- `GET /api/staff`: 获取人员列表
- `POST /api/staff`: 创建人员
- `PUT /api/staff/:id`: 更新人员信息
- `DELETE /api/staff/:id`: 删除人员

### 屏幕管理

- `GET /api/screens`: 获取屏幕配置
- `PUT /api/screens/:id`: 更新屏幕配置

### 系统设置

- `GET /api/settings`: 获取系统设置
- `PUT /api/settings`: 更新系统设置

## 维护和升级

系统支持以下维护和升级功能：

1. 账户管理: 可以添加、编辑和删除管理员账户
2. 规章制度更新: 可以在后台更新规章制度内容
3. 值班表导出: 支持将值班表导出为 Excel 或 PDF 格式
4. 屏幕设置: 可以自定义大屏幕显示的内容和样式

## 贡献者

- 系统开发团队
