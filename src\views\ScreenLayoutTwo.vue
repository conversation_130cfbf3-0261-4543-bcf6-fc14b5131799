<template>
  <div class="screen-container" ref="screenContainer">
    <div class="main-content">
      <!-- 隐藏的统计区域 - 保留所有功能但不显示 -->
      <div class="stats-row" style="display: none;">
        <div class="stat-box entry">
          <div class="stat-header">
            <span class="header-icon">◄</span>
            今日入区
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ inAreaData.total }}</div>
            <div class="stat-info">
              <div class="info-line">
                <span class="info-label">行政人员</span>
                <span class="info-value">{{ inAreaData.admin }}</span>
              </div>
              <div class="info-divider"></div>
              <div class="info-line">
                <span class="info-label">刑事人员</span>
                <span class="info-value">{{ inAreaData.criminal }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="stat-box exit">
          <div class="stat-header">
            <span class="header-icon">►</span>
            今日出区
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ outAreaData.total }}</div>
            <div class="stat-info">
              <div class="info-line">
                <span class="info-label">行政人员</span>
                <span class="info-value">{{ outAreaData.admin }}</span>
              </div>
              <div class="info-divider"></div>
              <div class="info-line">
                <span class="info-label">刑事人员</span>
                <span class="info-value">{{ outAreaData.criminal }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="content-wrapper">
        <div class="flow-section-full">
          <h2>执法办案管理中心工作流程</h2>
          
          <div class="flow-container">
            <div class="flow-chart">
              <img :src="flowChartImage" alt="执法办案管理中心工作流程图" />
            </div>
          </div>
        </div>
        
        <div class="work-tips-section">
          <div class="work-tips-header">
            <i class="el-icon-warning-outline"></i>
            <h2>办案民警工作提示</h2>
          </div>
          <div class="work-tips-content">
            <div class="work-tip-item">
              <div class="tip-number">1</div>
              <div class="tip-text">接、送涉案人必须由正式民警带队</div>
            </div>
            <div class="work-tip-item">
              <div class="tip-number">2</div>
              <div class="tip-text">办案区全域禁止吸烟</div>
            </div>
            <div class="work-tip-item">
              <div class="tip-number">3</div>
              <div class="tip-text">讯询问室内禁止：打骂侮辱被问人、单人讯询问、玩手机</div>
            </div>
            <div class="work-tip-item">
              <div class="tip-number">4</div>
              <div class="tip-text">对于怀孕或者正在哺乳自己不满一周岁婴儿的妇女、不满十六周岁的未成年人、已满七十周岁的老年人，应当在规定时间内讯问、询问完毕，不得送入候问室</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import flowChartImage from '../assets/办案中心.jpg';
import api from '../utils/api';

// 时间显示
const currentTime = ref(formatTime(new Date()));
let timer;

function formatTime(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 更新时间
function updateTime() {
  currentTime.value = formatTime(new Date());
}

// 出入区数据
const inAreaData = ref({
  total: 0,
  admin: 0,
  criminal: 0
});

const outAreaData = ref({
  total: 0,
  admin: 0,
  criminal: 0
});

// 模拟登录获取token
async function login() {
  try {
    const res = await api.login({ username: 'sjzsj01', password: 'admin@123' });
    if (res && res.token) {
      localStorage.setItem('token', res.token);
      return true;
    }
    return false;
  } catch (error) {
    console.error('登录失败:', error);
    return false;
  }
}

// 获取出入区数据
async function fetchInOutAreaData(placeId = '1') {  // 默认使用ID为1的场所
  try {
    const res = await api.getInOutAreaData(placeId);
    if (res && res.code === 200 && res.data) {
      const data = res.data;
      
      // 处理入区数据
      inAreaData.value = {
        total: data.jrrq?.personTotal || 0,
        admin: data.jrrq?.xzTotal || 0,
        criminal: data.jrrq?.xsTotal || 0
      };
      
      // 处理出区数据
      outAreaData.value = {
        total: data.jrcq?.personTotal || 0,
        admin: data.jrcq?.xzTotal || 0,
        criminal: data.jrcq?.xsTotal || 0
      };
    }
  } catch (error) {
    // console.error('获取出入区数据失败:', error);
  }
}

// 初始化数据并定时刷新
async function initData() {
  // 确保已登录
  if (!localStorage.getItem('token')) {
    await login();
  }
  
  // 获取初始数据
  await fetchInOutAreaData();
  
  // 定时刷新数据（每5分钟）
  setInterval(() => {
    fetchInOutAreaData();
  }, 5 * 60 * 1000);
}

// 全屏相关
const screenContainer = ref(null);
const isFullscreen = ref(false);

// 切换全屏模式
async function toggleFullscreen() {
  if (!document.fullscreenElement) {
    try {
      await screenContainer.value.requestFullscreen();
      isFullscreen.value = true;
    } catch (err) {
      console.error('无法进入全屏模式：', err);
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      isFullscreen.value = false;
    }
  }
}

onMounted(() => {
  timer = setInterval(updateTime, 1000);
  
  // 初始化数据
  initData();
  
  // 尝试自动进入全屏模式
  const enterFullscreen = async () => {
    try {
      if (screenContainer.value && !document.fullscreenElement) {
        await screenContainer.value.requestFullscreen();
        isFullscreen.value = true;
      }
    } catch (err) {
      console.error('自动进入全屏模式失败：', err);
    }
  };
  
  // 延迟1秒后尝试进入全屏模式
  setTimeout(enterFullscreen, 1000);
  
  // 监听键盘事件，F11全屏切换
  const handleKeyDown = (event) => {
    if (event.key === 'F11') {
      event.preventDefault();
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
});

onUnmounted(() => {
  clearInterval(timer);
  // 退出全屏
  if (document.fullscreenElement) {
    document.exitFullscreen();
  }
});
</script>

<style scoped>
.screen-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: clamp(20px, 2vw, 60px);
  box-sizing: border-box;
  gap: clamp(20px, 2vh, 40px);
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 内容包装区 - 充满整个屏幕 */
.content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: row;
  gap: clamp(2px, 1vw, 50px);
  overflow: hidden;
  padding: clamp(1px, 0.1vh, 3px);
  height: 100%;
}

/* 顶部进出区统计 */
.stats-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  height: 140px;
  min-height: 140px;
  margin-bottom: 5px;
  overflow: hidden;
}

.stat-box {
  flex: 1;
  background: linear-gradient(to bottom, rgba(40, 40, 45, 0.95), rgba(20, 20, 25, 0.95));
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.entry {
  border-left: 3px solid #2997ff;
}

.exit {
  border-left: 3px solid #f56300;
}

.stat-header {
  display: flex;
  align-items: center;
  color: #ffffff;
  font-size: 20px;
  font-weight: bold;
  text-align: left;
  margin-bottom: 5px;
}

.header-icon {
  margin-right: 6px;
  font-size: 16px;
}

.entry .header-icon,
.entry .stat-header {
  color: #2997ff;
}

.exit .header-icon,
.exit .stat-header {
  color: #f56300;
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  width: 100%;
}

.stat-value {
  font-size: 53px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
}

.stat-info {
  background: rgba(0, 0, 0, 0.2);
  padding: 5px 10px;
  border-radius: 6px;
}

.info-line {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
  margin: 0;
  white-space: nowrap;
}

.info-label {
  font-size: 22px;
  color: rgba(255, 255, 255, 0.85);
}

.info-value {
  font-size: 27px;
  font-weight: bold;
  color: #ffffff;
}

.info-divider {
  width: 90%;
  height: 1px;
  background: rgba(255, 255, 255, 0.15);
  margin: 2px 0;
}

.entry .info-value {
  color: #5aafff;
}

.exit .info-value {
  color: #ff8c40;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 主流程区域 - 占据更大空间 */
.flow-section-full {
  flex: 2.2; /* 调整宽度比例 */
  background: rgba(255, 255, 255, 0.95);
  border-radius: clamp(20px, 2vw, 40px);
  padding: clamp(20px, 3vh, 60px) clamp(30px, 4vw, 80px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

h2 {
  color: #333333;
  text-align: center;
  margin: 0 0 clamp(18px, 2.2vh, 28px) 0;
  padding: 0 0 clamp(15px, 1.8vh, 25px) 0;
  border-bottom: 2px solid rgba(51, 51, 51, 0.2);
  font-size: clamp(28px, 3.5vw, 42px);
  letter-spacing: -0.01em;
  font-weight: 700;
}

.flow-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.flow-chart {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  height: 100%;
  padding: 0;
  margin: 0;
}

.flow-chart img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: clamp(15px, 1.8vw, 25px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(41, 151, 255, 0.4);
  transition: all 0.4s ease;
}

.flow-chart img:hover {
  transform: scale(1.03);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.6);
  border-color: rgba(41, 151, 255, 0.6);
}

/* 工作提示区域 - 更大更醒目 */
.work-tips-section {
  flex: 1.2; /* 增加宽度比例 */
  background: rgba(255, 255, 255, 0.95);
  border-radius: clamp(20px, 2vw, 40px);
  padding: clamp(20px, 3vh, 60px) clamp(30px, 4vw, 80px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-height: 100%;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.work-tips-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: clamp(25px, 3vh, 40px);
  padding-bottom: clamp(20px, 2.5vh, 35px);
  border-bottom: 2px solid rgba(51, 51, 51, 0.2);
}

.work-tips-header i {
  font-size: clamp(32px, 4vw, 48px);
  color: #dc2626;
  margin-right: clamp(15px, 1.8vw, 25px);
}

.work-tips-header h2 {
  margin: 0;
  padding: 0;
  border: none;
  color: #dc2626;
  font-size: clamp(32px, 4vw, 48px);
  font-weight: 800;
  letter-spacing: -0.01em;
}

.work-tips-content {
  display: flex;
  flex-direction: column;
  gap: clamp(2px, 1vh, 35px);
  overflow-y: auto;
  padding-right: clamp(10px, 1.2vw, 16px);
}

.work-tips-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.work-tips-content::-webkit-scrollbar-track {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 5px;
}

.work-tips-content::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 128, 0.4);
  border-radius: 5px;
  transition: background 0.2s ease;
}

.work-tips-content::-webkit-scrollbar-thumb:hover {
  background: rgba(120, 120, 128, 0.6);
}

.work-tip-item {
  display: flex;
  align-items: flex-start;
  background: rgba(220, 38, 38, 0.1);
  border-radius: clamp(12px, 1.5vw, 20px);
  padding: clamp(15px, 2vh, 25px);
  border: 1px solid rgba(220, 38, 38, 0.2);
  transition: all 0.3s ease;
}

.work-tip-item:hover {
  background: rgba(220, 38, 38, 0.15);
  border-color: rgba(220, 38, 38, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.2);
}

.tip-number {
  width: clamp(28px, 3.5vw, 40px);
  height: clamp(28px, 3.5vw, 40px);
  border-radius: 50%;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  font-weight: 700;
  font-size: clamp(16px, 2vw, 24px);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: clamp(12px, 1.5vw, 20px);
  flex-shrink: 0;
  box-shadow: 0 3px 10px rgba(220, 38, 38, 0.3);
}

.tip-text {
  font-size: clamp(22px, 2.8vw, 32px);
  line-height: 1.4;
  color: #333333;
  flex: 1;
  word-break: break-all;
  white-space: normal;
  letter-spacing: -0.01em;
  font-weight: 500;
}

/* 全屏按钮 */
.fullscreen-btn {
  background: rgba(42, 201, 167, 0.1);
  color: #2ac9a6;
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  transition: all 0.2s;
  font-weight: 500;
}

.fullscreen-btn:hover {
  background: rgba(42, 201, 167, 0.2);
}

.fullscreen-btn i {
  margin-right: 6px;
}

/* 适配小屏幕 */
@media (max-width: 1024px) {
  .stats-row {
    flex-wrap: wrap;
    height: auto;
  }
  
  .stat-box {
    min-width: calc(50% - 8px);
  }
  
  .content-wrapper {
    flex-direction: column;
  }
}
</style> 